{"name": "cantika-website", "version": "1.0.0", "scripts": {"build:css": "tailwindcss -i src/input.css -o dist/output.css", "copy:html": "copyfiles *.html dist", "copy:assets": "copyfiles assets/**/* dist", "build": "npm-run-all build:css copy:html copy:assets", "dev": "tailwindcss -i src/input.css -o dist/output.css --watch"}, "devDependencies": {"autoprefixer": "^10.4.21", "copyfiles": "^2.4.1", "npm-run-all": "^4.1.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.14"}}